package main

import (
	"fmt"
	"github.com/oxio/aia/internal/ui"
	log "github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"strings"
	"time"
)

// SlowReader is a reader that reads slowly from another reader
type SlowReader struct {
	reader    io.Reader
	chunkSize int
	delay     time.Duration
	nextDelay time.Duration
}

// NewSlowReader creates a new slow reader
func NewSlowReader(reader io.Reader, chunkSize int, delay time.Duration, initialDelay time.Duration) *SlowReader {
	return &SlowReader{
		reader:    reader,
		chunkSize: chunkSize,
		delay:     delay,
		nextDelay: initialDelay,
	}
}

// Read implements the io.Reader interface
func (r *SlowReader) Read(p []byte) (n int, err error) {
	// Limit the read size to chunkSize
	readSize := len(p)
	if readSize > r.chunkSize {
		readSize = r.chunkSize
	}

	// Read from the underlying reader
	n, err = r.reader.Read(p[:readSize])

	// Simulate delay
	time.Sleep(r.nextDelay)
	r.nextDelay = r.delay

	return n, err
}

func main() {

	log.SetOutput(&lumberjack.Logger{
		Filename:   "/tmp/aia-stream.log",
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     356,
		Compress:   true,
	})

	log.SetFormatter(&log.TextFormatter{})
	log.SetLevel(log.DebugLevel)

	defer func() {
		log.Debug("quitting streamtest")
	}()
	log.Debug("starting streamtest")

	fmt.Println("Testing streaming teaframe with io.Reader")

	// Create a long string
	//userText := "This is a streaming test with a slow reader.\n" +
	//	"Line 10: This is the tenth line of content."
	//
	longText1 := `<think>This is thinking process emulation of the LLM.
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
This is a long process. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
</think>

# title

## Subtitle

This is a streaming test with a slow reader.
The content will be displayed gradually as it's read :wink:
Each chunk will be read with a small delay to simulate streaming. Lorem ipsum ` + "`dolor sit amet`" + `, consectetur adipiscing elit. Dolor sit amet, consectetur adipiscing elit.

Lorem

` + "```php" + `
<?php
$msg = 'Hello';
echo $msg;
` + "```" + `

## Test

> This is a **quote**

- _Antonio De Marco_

Here is a link to a fan: [link](https://allegro.pl/oferta/wiatrak-usb-cichy-mini-wentylator-biurkowy-chlodzenie-topk-k50-niebieski-14257280469)

---

### This is a table:
| Item              | In Stock | Price |
| :---------------- | :------: | ----: |
| Python Hat        |   True   | 23.99 |
| SQL Hat           |   True   | 23.99 |
| Codecademy Tee    |  False   | 19.99 |
| Codecademy Hoodie |  False   | 42.99 |
| Codecademy Hoodie |  False   | 42.99 |

### This is a numbered list:
1. Line 1: This is the first line of content.
1. Line 2: This is the second line of content.
1. Line 3: This is the third line of content.
1. Line 4: This is the fourth line of content.
1. Line 5: This is the fifth line of content.
1. Line 6: This is the sixth line of content.
1. Line 7: This is the seventh line of content.
1. Line 8: This is the eighth line of content.

## Last title
1. Line 10: This is the tenth line of content.`

	longText1 += "\n"

	longText2 := "This is a streaming test with a slow reader.\n" +
		"The content will be displayed gradually as it's read.\n" +
		"Each chunk will be read with a small delay to simulate streaming.\n" +
		"Lorem ipsum dolor sit amet, consectetur adipiscing elit.\n" +
		"Dolor sit amet, consectetur adipiscing elit.\n\n"

	for i := 0; i < 20; i++ {
		longText2 += fmt.Sprintf("Line %d: This is the %dth line of content.\n", i, i)
	}

	//errorText := "There was an error\n" +
	//	"Something went wrong."
	//
	//bold := lipgloss.NewStyle().Bold(true)
	//initialText := bold.Render("Hey") + ", how can " + bold.Render("AI A") + "ssist you?"

	//ui.ShowUserMessage(userText)

	longText3 := `One One One
Two Two Two
Three Three Three
Four Four Four  
FiveFiveFive

Seven Seven Seven 
Eight Eight Eight
Nine Nine Nine
Ten`

	//	longText3 = `One One One
	//Two Two Two
	//Three Three Three
	//Four Four Four
	//FiveFiveFive
	//Six`

	longText3 += ""

	//ui.StreamMessage(
	//	NewSlowReader(strings.NewReader(longText2), 10, 20*time.Millisecond, 1*time.Second),
	//	ui.AiaStyle().WithRichText(),
	//)
	ui.StreamMessage(NewSlowReader(strings.NewReader(longText3), 10, 200*time.Millisecond, 0), ui.AiaStyle())
	//ui.StreamMessage(NewSlowReader(strings.NewReader(longText1), 10, 20*time.Millisecond, 0), ui.AiaStyle().WithRichText())

	//ui.StreamMessage(NewSlowReader(strings.NewReader(longText2), 10, 20*time.Millisecond, 0), ui.AiaStyle())
	//ui.ShowUserMessage(userText)
	//ui.ShowMessage(longText2, ui.AiaStyle())
	//ui.ShowErrorMessage(errorText)
	//ui.ShowWarningMessage("This is a warning")
	//ui.ShowInfoMessage("# h1\n## h2\nThis is a `notice`")
	//ui.ShowSuccessMessage("This operation succeeded!")
	//ui.ShowAiaMessage(initialText)

	fmt.Println("\nAll streaming tests completed successfully!")
}
