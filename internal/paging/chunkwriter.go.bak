package paging

import (
	log "github.com/sirupsen/logrus"
	"strings"
)

// ChunkedLineWriter processes terminal output into chunks based on visible line count.
type ChunkedLineWriter struct {
	linesPerChunk int
	chunks        []string
	partialLine   string
}

// NewChunkedLineWriter constructs a writer with chunking by lines.
func NewChunkedLineWriter(linesPerChunk int) *ChunkedLineWriter {
	if linesPerChunk <= 0 {
		panic("linesPerChunk must be greater than 0")
	}
	return &ChunkedLineWriter{
		linesPerChunk: linesPerChunk,
		chunks:        make([]string, 0),
	}
}

func (clw *ChunkedLineWriter) Reset() {
	clw.partialLine = ""
	clw.chunks = make([]string, 0)
}

// Write implements io.Writer and processes input into visible line chunks.
func (clw *ChunkedLineWriter) Write(p []byte) (n int, err error) {
	input := clw.partialLine + string(p)
	lines := splitPreserveNewline(input)
	if len(lines) == 0 {
		return len(p), nil
	}

	log.Debug("C: Write: ", string(p))
	log.Debug("C: Input: ", input)
	log.Debug("C: Lines: ", len(lines))

	clw.partialLine = ""

	// If the input doesn't end with a newline, buffer the last partial line
	if !strings.HasSuffix(input, "\n") {
		clw.partialLine = lines[len(lines)-1]
		lines = lines[:len(lines)-1]
	}

	for _, line := range lines {
		// Ensure we have at least one chunk to work with
		if len(clw.chunks) == 0 {
			clw.chunks = append(clw.chunks, "")
		}

		// Add line to the last chunk
		clw.chunks[len(clw.chunks)-1] += line

		// Check if the last chunk has reached the line limit
		if clw.visibleLineCountInLastChunk() >= clw.linesPerChunk {
			// Only create a new chunk if there are more lines to process
			// or if this isn't the last line in the current batch
			clw.chunks = append(clw.chunks, "")
		}
	}

	return len(p), nil
}

// Close flushes any remaining buffered content as a chunk.
func (clw *ChunkedLineWriter) Close() error {
	if clw.partialLine != "" {
		// Ensure we have at least one chunk to work with
		if len(clw.chunks) == 0 {
			clw.chunks = append(clw.chunks, "")
		}
		// Add the partial line to the last chunk
		clw.chunks[len(clw.chunks)-1] += clw.partialLine
		clw.partialLine = ""
	}
	return nil
}

// visibleLineCountInLastChunk counts actual displayed lines in the last chunk
func (clw *ChunkedLineWriter) visibleLineCountInLastChunk() int {
	if len(clw.chunks) == 0 {
		return 0
	}
	lastChunk := clw.chunks[len(clw.chunks)-1]
	return strings.Count(lastChunk, "\n")
}

// Chunks returns all the chunks that have been processed, excluding empty chunks.
func (clw *ChunkedLineWriter) Chunks() []string {
	var result []string
	for _, chunk := range clw.chunks {
		if chunk != "" {
			result = append(result, chunk)
		}
	}
	return result
}

// AllChunks returns all the chunks that have been processed, excluding empty chunks.
func (clw *ChunkedLineWriter) AllChunks() []string {
	var result []string
	for _, chunk := range clw.chunks {
		if chunk != "" {
			result = append(result, chunk)
		}
	}
	if len(clw.partialLine) > 0 {
		log.Debug("C: AllChunks: Adding partial line: ", clw.partialLine)
		result = append(result, clw.partialLine)
	}
	return result
}

// splitPreserveNewline splits input into lines keeping the newline char.
func splitPreserveNewline(s string) []string {
	var lines []string
	var buf strings.Builder

	for i := 0; i < len(s); i++ {
		ch := s[i]
		buf.WriteByte(ch)

		if ch == '\n' {
			lines = append(lines, buf.String())
			buf.Reset()
		}
	}
	if buf.Len() > 0 {
		lines = append(lines, buf.String())
	}
	return lines
}
